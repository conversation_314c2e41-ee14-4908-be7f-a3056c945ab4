<script lang="ts">
  export let href: string;
  export let ariaLabel: string = 'Add new item';
</script>

<a
  {href}
  class="floating-action-btn"
  aria-label={ariaLabel}
>
  <div class="btn-content">
    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
    </svg>
  </div>
</a>

<style>
  .floating-action-btn {
    @apply md:hidden fixed bottom-24 right-4 z-40 w-16 h-16 rounded-full flex items-center justify-center;
    @apply transition-all duration-300 ease-out;
    @apply focus:outline-none focus:ring-4 focus:ring-blue-200/40;

    /* Modern glassmorphism effect with professional blue accent */
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.9), rgba(37, 99, 235, 0.95));
    backdrop-filter: blur(20px);
    border: 1px solid rgba(59, 130, 246, 0.3);
    box-shadow:
      0 8px 32px rgba(59, 130, 246, 0.25),
      0 2px 8px rgba(37, 99, 235, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);

    /* Subtle pulse animation */
    animation: subtle-pulse 3s ease-in-out infinite;
  }

  .floating-action-btn:hover {
    transform: translateY(-2px) scale(1.05);
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.95), rgba(29, 78, 216, 0.98));
    box-shadow:
      0 12px 40px rgba(59, 130, 246, 0.3),
      0 4px 12px rgba(37, 99, 235, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.25);
  }

  .floating-action-btn:active {
    transform: translateY(0) scale(0.98);
    background: linear-gradient(135deg, rgba(29, 78, 216, 0.98), rgba(30, 64, 175, 0.95));
    box-shadow:
      0 4px 16px rgba(59, 130, 246, 0.2),
      0 1px 4px rgba(37, 99, 235, 0.12),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
  }

  .btn-content {
    @apply text-white transition-all duration-300;
    transform: translateZ(0);
  }

  .floating-action-btn:hover .btn-content {
    @apply text-white;
    transform: rotate(90deg);
  }

  @keyframes subtle-pulse {
    0%, 100% {
      box-shadow:
        0 8px 32px rgba(59, 130, 246, 0.25),
        0 2px 8px rgba(37, 99, 235, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        0 0 0 0 rgba(59, 130, 246, 0);
    }
    50% {
      box-shadow:
        0 8px 32px rgba(59, 130, 246, 0.25),
        0 2px 8px rgba(37, 99, 235, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        0 0 0 8px rgba(59, 130, 246, 0.2);
    }
  }

  /* Ensure smooth performance */
  .floating-action-btn {
    will-change: transform, box-shadow;
    backface-visibility: hidden;
  }
</style>
